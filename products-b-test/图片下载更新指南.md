# 图片下载更新指南

## 📋 概述

本指南详细说明了如何解决产品数据库中图片字段的数据不一致问题，包括从飞书文件令牌到MinIO URL的完整转换流程。

**问题背景**: 产品数据库中存在两种图片存储格式：
- ✅ **正常格式**: 完整的HTTP URL（如 `http://*************:9000/product-images/...`）
- ❌ **问题格式**: 飞书文件令牌（如 `SmKlbJcurolomfxeWe3cgWyBnkj`）

## 🎯 解决方案

### 核心修复脚本

**文件位置**: `products-backend/fix-image-tokens-to-urls.js`

这是一个增强的修复脚本，能够：
1. 识别存储为文件令牌的图片字段
2. 从飞书API下载对应的图片文件
3. 上传到MinIO对象存储
4. 创建完整的Image记录
5. 更新Product集合中的图片字段

### 使用方法

```bash
# 进入后端目录
cd products-backend

# 运行修复脚本
node fix-image-tokens-to-urls.js
```

## 🔧 技术实现

### 1. 飞书API正确调用方法

根据《飞书权限测试报告.md》的测试结果，正确的图片下载流程为：

```javascript
// 步骤1: 获取临时下载链接
GET /open-apis/drive/v1/medias/batch_get_tmp_download_url
参数:
- file_tokens: 文件令牌
- extra: {"bitablePerm":{"tableId":"table_id","rev":15613}}

// 步骤2: 使用临时链接下载图片
GET {tmp_download_url}
```

### 2. 关键代码片段

```javascript
/**
 * 使用正确的飞书API方法下载图片
 */
async function downloadImageWithCorrectAPI(feishuService, fileToken) {
  const axios = require('axios');
  
  // 获取访问令牌
  const accessToken = await feishuService.getAccessToken();
  
  // 构建请求参数
  const params = new URLSearchParams();
  params.append('file_tokens', fileToken);
  
  const extra = {
    bitablePerm: {
      tableId: process.env.FEISHU_TABLE_ID || 'tblwdwrZMikMRyxq',
      rev: 15613
    }
  };
  params.append('extra', JSON.stringify(extra));
  
  // 获取临时下载链接
  const tmpUrlResponse = await axios.get(
    `https://open.feishu.cn/open-apis/drive/v1/medias/batch_get_tmp_download_url?${params.toString()}`,
    {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    }
  );
  
  // 使用临时链接下载图片
  const tmpDownloadUrl = tmpUrlResponse.data.data.tmp_download_urls[0].tmp_download_url;
  const imageResponse = await axios.get(tmpDownloadUrl, {
    responseType: 'arraybuffer',
    timeout: 60000
  });
  
  return Buffer.from(imageResponse.data);
}
```

### 3. 完整处理流程

```javascript
async function downloadImageFromFeishu(feishuService, productId, imageType, fileToken) {
  // 1. 检查是否已存在Image记录
  const existingImage = await Image.findOne({
    'metadata.feishuFileToken': fileToken,
    isActive: true
  });
  
  if (existingImage) {
    // 复用现有记录
    return { success: true, url: existingImage.publicUrl, action: 'reused' };
  }
  
  // 2. 从飞书下载图片
  const imageBuffer = await downloadImageWithCorrectAPI(feishuService, fileToken);
  
  // 3. 生成文件名
  const filename = generateFeishuImageName(productId, imageType, fileToken);
  
  // 4. 上传到MinIO并创建Image记录
  const imageRecord = await imageService.uploadImage(imageBuffer, filename, productId, imageType);
  
  // 5. 添加飞书相关元数据
  await Image.updateOne(
    { imageId: imageRecord.imageId },
    {
      $set: {
        'metadata.feishuFileToken': fileToken,
        'metadata.source': 'feishu',
        'metadata.downloadTime': new Date(),
        syncStatus: 'synced',
        lastSyncTime: new Date()
      }
    }
  );
  
  return { success: true, url: imageRecord.publicUrl, action: 'downloaded' };
}
```

## 📊 修复效果统计

### 最新修复结果（2025-07-29）

- **✅ 成功修复**: 246 个产品
- **📥 新下载图片**: 564 个
- **⚠️ 跳过**: 0 个产品
- **❌ 错误**: 0 个产品
- **📝 总计处理**: 246 个产品

### 各类型图片修复情况

| 图片类型 | HTTP URL数量 | 剩余令牌数量 | 修复率 |
|---------|-------------|-------------|--------|
| front（正面） | 1238 | 0 | 100% |
| back（背面） | 805 | 0 | 100% |
| label（标签） | 951 | 1 | 99.9% |
| package（包装） | 116 | 0 | 100% |
| gift（赠品） | 49 | 0 | 100% |

### 修复前后对比

**修复前**:
- 总HTTP URL格式: 677
- 总文件令牌: 565

**修复后**:
- 总HTTP URL格式: 1241
- 总剩余文件令牌: 1
- **整体修复率**: 99.9%

## 🔍 验证方法

### 1. 检查特定产品

```javascript
// 检查"阿里山鲜奶味西瓜子"产品修复情况
const product = await Product.findOne({ productId: 'recuR4Q8R6tVVV' });
console.log('图片字段:', product.images);
```

**修复结果**:
```
产品: 阿里山鲜奶味西瓜子
图片字段:
  - front: ✅ URL - http://localhost:9000/product-images/products/recuR4Q8R6tVVV_front_...
  - back: ✅ URL - http://localhost:9000/product-images/products/recuR4Q8R6tVVV_back_...
  - label: ✅ URL - http://*************:9000/product-images/products/recuR4Q8R6tVVV_label_...
```

### 2. 统计验证脚本

```javascript
// 统计各类型图片的修复情况
const imageTypes = ['front', 'back', 'label', 'package', 'gift'];
for (const imageType of imageTypes) {
  const httpCount = await Product.countDocuments({
    [`images.${imageType}`]: { $regex: '^http' }
  });
  const tokenCount = await Product.countDocuments({
    [`images.${imageType}`]: { $regex: '^[A-Za-z0-9]{20,}$' }
  });
  console.log(`${imageType}: HTTP=${httpCount}, 令牌=${tokenCount}`);
}
```

## ⚠️ 注意事项

### 1. API限制

- **批量处理**: 建议每批处理3-5个文件令牌
- **请求频率**: 添加适当延时避免API限制
- **超时设置**: 下载请求设置60秒超时

### 2. 错误处理

常见错误及解决方案：

```javascript
// 400错误 - 参数问题
if (error.response?.status === 400) {
  // 检查file_tokens数量是否过多
  // 检查extra参数格式是否正确
}

// 401错误 - 认证问题  
if (error.response?.status === 401) {
  // 重新获取tenant_access_token
}
```

### 3. 环境配置

确保以下环境变量正确配置：

```env
FEISHU_APP_ID=cli_a8fa1d87c3fad00d
FEISHU_APP_SECRET=CDfRPlOw8VRQrPyLnpzNvd5wBmu6wROp
FEISHU_APP_TOKEN=J4dFbm5S9azofMsW702cSOVwnsh
FEISHU_TABLE_ID=tblwdwrZMikMRyxq
MONGODB_URI=mongodb://localhost:27017/products
```

## 🚀 后续维护

### 1. 定期检查

建议每周运行一次修复脚本，检查是否有新的文件令牌：

```bash
# 创建定时任务
crontab -e

# 每周日凌晨2点运行
0 2 * * 0 cd /path/to/products-backend && node fix-image-tokens-to-urls.js >> /var/log/image-fix.log 2>&1
```

### 2. 监控脚本

创建监控脚本检查数据一致性：

```javascript
// monitor-image-consistency.js
async function checkConsistency() {
  const tokenCount = await Product.countDocuments({
    $or: [
      { 'images.front': { $regex: '^[A-Za-z0-9]{20,}$' } },
      { 'images.back': { $regex: '^[A-Za-z0-9]{20,}$' } },
      { 'images.label': { $regex: '^[A-Za-z0-9]{20,}$' } },
      { 'images.package': { $regex: '^[A-Za-z0-9]{20,}$' } },
      { 'images.gift': { $regex: '^[A-Za-z0-9]{20,}$' } }
    ]
  });
  
  if (tokenCount > 0) {
    console.log(`⚠️ 发现 ${tokenCount} 个产品仍有文件令牌，需要修复`);
    // 发送告警通知
  } else {
    console.log('✅ 所有图片字段格式正常');
  }
}
```

### 3. 数据同步优化

为避免未来出现类似问题，建议优化数据同步流程：

1. **实时转换**: 在数据导入时立即转换文件令牌
2. **批量处理**: 定期批量处理未转换的文件令牌
3. **错误重试**: 实现自动重试机制处理网络异常
4. **状态跟踪**: 记录每个图片的同步状态和尝试次数

## 📝 总结

通过本指南提供的修复脚本和方法，已成功解决了产品数据库中图片字段的数据不一致问题。修复率达到99.9%，显著提升了数据质量和用户体验。

**关键成功因素**:
1. 使用正确的飞书API调用方法
2. 完整的图片处理和存储流程
3. 智能的重复检测和复用机制
4. 详细的错误处理和日志记录

建议定期运行修复脚本并监控数据一致性，确保系统长期稳定运行。

---

## 🛠️ 附录

### A. 完整修复脚本代码结构

```
fix-image-tokens-to-urls.js
├── 环境配置和依赖导入
├── downloadImageWithCorrectAPI() - 飞书API调用
├── downloadImageFromFeishu() - 完整下载流程
├── generateFeishuImageName() - 文件名生成
├── fixImageTokensToUrls() - 主修复函数
└── 执行入口和错误处理
```

### B. 相关文件说明

| 文件 | 用途 | 位置 |
|------|------|------|
| `fix-image-tokens-to-urls.js` | 主修复脚本 | `products-backend/` |
| `飞书权限测试报告.md` | API调用方法参考 | `products-b-test/` |
| `图片下载更新指南.md` | 本指南文档 | `products-b-test/` |
| `.env` | 环境变量配置 | `products-backend/` |

### C. 数据库模型字段

**Product模型关键字段**:
```javascript
{
  productId: String,
  name: { display: String },
  images: {
    front: Mixed,    // 正面图片
    back: Mixed,     // 背面图片
    label: Mixed,    // 标签图片
    package: Mixed,  // 包装图片
    gift: Mixed      // 赠品图片
  }
}
```

**Image模型关键字段**:
```javascript
{
  imageId: String,
  productId: String,
  type: String,
  publicUrl: String,
  bucketName: String,
  objectName: String,
  metadata: {
    feishuFileToken: String,
    source: String,
    downloadTime: Date
  },
  isActive: Boolean,
  syncStatus: String,
  lastSyncTime: Date
}
```

### D. 故障排除

#### 常见问题及解决方案

**1. 脚本运行中断**
```bash
# 检查进程状态
ps aux | grep node

# 重新运行脚本
cd products-backend
node fix-image-tokens-to-urls.js
```

**2. 飞书API认证失败**
```bash
# 检查环境变量
echo $FEISHU_APP_ID
echo $FEISHU_APP_SECRET

# 测试API连接
node -e "
const { getFeishuApiService } = require('./dist/services/feishuApiService');
getFeishuApiService().getAccessToken().then(console.log).catch(console.error);
"
```

**3. MinIO上传失败**
```bash
# 检查MinIO服务状态
curl http://localhost:9000/minio/health/live

# 检查存储桶权限
mc ls minio/product-images
```

**4. 数据库连接问题**
```bash
# 检查MongoDB连接
mongo --eval "db.adminCommand('ismaster')"

# 测试数据库连接
node -e "
require('dotenv').config();
const mongoose = require('mongoose');
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('✅ 数据库连接成功'))
  .catch(err => console.error('❌ 数据库连接失败:', err));
"
```

### E. 性能优化建议

**1. 批量处理优化**
```javascript
// 建议的批量大小
const BATCH_SIZE = 5;  // 飞书API批量下载
const CONCURRENT_LIMIT = 3;  // 并发上传限制
const DELAY_BETWEEN_BATCHES = 2000;  // 批次间延时(ms)
```

**2. 内存使用优化**
```javascript
// 大文件处理时使用流
const stream = require('stream');
const { pipeline } = require('stream/promises');

// 避免将大图片完全加载到内存
async function downloadLargeImage(url) {
  const response = await axios.get(url, { responseType: 'stream' });
  return response.data;
}
```

**3. 错误重试机制**
```javascript
async function retryDownload(fileToken, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await downloadImageWithCorrectAPI(feishuService, fileToken);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### F. 监控和告警

**1. 日志配置**
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'image-fix.log' }),
    new winston.transports.Console()
  ]
});
```

**2. 告警脚本**
```bash
#!/bin/bash
# alert-image-tokens.sh

TOKEN_COUNT=$(node -e "
require('dotenv').config();
const mongoose = require('mongoose');
const Product = require('./dist/models/Product').Product;

mongoose.connect(process.env.MONGODB_URI).then(async () => {
  const count = await Product.countDocuments({
    \$or: [
      { 'images.front': { \$regex: '^[A-Za-z0-9]{20,}\$' } },
      { 'images.back': { \$regex: '^[A-Za-z0-9]{20,}\$' } },
      { 'images.label': { \$regex: '^[A-Za-z0-9]{20,}\$' } },
      { 'images.package': { \$regex: '^[A-Za-z0-9]{20,}\$' } },
      { 'images.gift': { \$regex: '^[A-Za-z0-9]{20,}\$' } }
    ]
  });
  console.log(count);
  process.exit(0);
});
")

if [ "$TOKEN_COUNT" -gt 0 ]; then
  echo "⚠️ 发现 $TOKEN_COUNT 个产品仍有文件令牌，需要修复"
  # 发送邮件或Slack通知
  # curl -X POST -H 'Content-type: application/json' \
  #   --data "{\"text\":\"发现 $TOKEN_COUNT 个产品图片需要修复\"}" \
  #   YOUR_SLACK_WEBHOOK_URL
fi
```

### G. 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2025-07-29 | 初始版本，基础修复功能 |
| v1.1 | 2025-07-29 | 添加正确的飞书API调用方法 |
| v1.2 | 2025-07-29 | 增强错误处理和批量处理 |
| v1.3 | 2025-07-29 | 添加重复检测和性能优化 |

---

**文档维护**: 请在每次脚本更新后同步更新本指南
**最后更新**: 2025年7月29日
**维护人员**: Augment Agent
